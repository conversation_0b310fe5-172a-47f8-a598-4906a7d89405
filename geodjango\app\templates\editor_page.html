{% extends 'header_editorpage.html' %}
{% load static %}


{% block content_map %}
<style>
    @import url('https://fonts.googleapis.com/css2?family=Sarabun:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap');
</style>

<link
  rel="stylesheet"
  href="https://unpkg.com/@geoman-io/leaflet-geoman-free@latest/dist/leaflet-geoman.css"
/>

<!-- css -->
<style>
    .text_map1mm {
        font-family: "Sarabun", sans-serif;
        font-weight: 100;
        font-style: normal;
        font-size: 13px;
        color: #010101;
        text-align: center;
        line-height: 11px;
        display: flex;
        align-items: center;  /* Aligns items vertically in the center */
        justify-content: center;  /* Aligns items horizontally in the center */
        height: 100%;  /* Ensure the flex container takes full height of its parent */
    }
    .text_map2mm {
        font-family: "Sarabun", sans-serif;
        font-weight: 100;
        font-style: normal;
        font-size: 16px;
        color: #010101;
        text-align: center;
        line-height: 14px;
        display: flex;
        align-items: center;  /* Aligns items vertically in the center */
        justify-content: center;  /* Aligns items horizontally in the center */
        height: 100%;  /* Ensure the flex container takes full height of its parent */
    }

    .text_no_parcel {
        font-family: "Sarabun", sans-serif;
        font-weight: 100;
        font-style: normal;
        font-size: 13px;
        color: #010101;
        text-align: center;
        line-height: 11px;
        display: flex;
        align-items: center;  /* Aligns items vertically in the center */
        justify-content: center;  /* Aligns items horizontally in the center */
        height: 100%;  /* Ensure the flex container takes full height of its parent */
    }
    /* map */
    #map {
        /* height 100% from windows */
        /* height: 100vh; */
        height: 82.5vh;
        background: #ffffff;
        outline: 0;

    }
</style>
<!-- // Include turf.js in your HTML -->
<script src="https://cdn.jsdelivr.net/npm/@turf/turf@6.5.0"></script>
<div class="toolbar py-5 py-lg-5" id="kt_toolbar">
    <!--begin::Container-->
    <div id="kt_toolbar_container" class=" container-fluid  d-flex flex-stack flex-wrap">

        <!--begin::Page title-->
        <div class="page-title d-flex flex-column me-3">
            <!--begin::Title-->
            <h1 class="d-flex text-gray-900 fw-bold my-1 fs-3">
                เขียนแผนที่
            </h1>
            <!--end::Title-->


            <!--begin::Breadcrumb-->
            <ul class="breadcrumb breadcrumb-dot fw-semibold text-gray-600 fs-7 my-1">
                <!--begin::Item-->
                <li class="breadcrumb-item text-gray-600">
                    <a href="#" class="text-gray-600 text-hover-primary">
                        Home </a>
                </li>
                <!--end::Item-->
                <!--begin::Item-->
                <li class="breadcrumb-item text-gray-500">
                    แปลงหมายเลข .... ภาษาตำบล ....  </li>
                <!--end::Item-->
            </ul>
            <!--end::Breadcrumb-->
        </div>
        <!--end::Page title-->
        <!--begin::Actions-->
        <div class="d-flex align-items-center py-2 py-md-1">
            <!--begin::Wrapper-->
            <div class="me-0">
                <button class="btn btn-primary fw-bold me-2 " data-kt-menu-trigger="click" data-kt-menu-placement="bottom-end">
                    ขึ้นรูปแปลง
                </button>
                <!--begin::Menu 3-->
                <div class="menu menu-sub menu-sub-dropdown menu-column menu-rounded menu-gray-800 menu-state-bg-light-primary fw-bold w-200px py-3" data-kt-menu="true">
                    <!--begin::Heading-->
                    <!-- <div class="menu-item px-3">
                        <div class="menu-content text-muted pb-2 px-3 fs-7 text-uppercase">Payments</div>
                    </div> -->
                    <!--end::Heading-->
                    <!--begin::Menu item-->
                    <div class="menu-item px-3" data-kt-menu-trigger="hover" data-kt-menu-placement="right-end">
                        <a href="#" class="menu-link px-3">
                            <span class="menu-title">ขีดเขตแยก</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <!--begin::Menu sub-->
                        <div class="menu-sub menu-sub-dropdown w-175px py-4">
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="#" class="menu-link px-3" id="add_side_plot_lines">เพิ่ม</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="#" class="menu-link px-3" id="edit_side_plot_lines">แก้ไข</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="#" class="menu-link px-3" id="del_side_plot_lines">ลบ</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu separator-->
                            <!-- <div class="separator my-2"></div> -->
                            <!--end::Menu separator-->

                        </div>
                        <!--end::Menu sub-->
                    </div>
                    <!--end::Menu item-->
                    <!--begin::Menu item-->
                    <div class="menu-item px-3" data-kt-menu-trigger="hover" data-kt-menu-placement="right-end">
                        <a href="#" class="menu-link px-3">
                            <span class="menu-title">หมุดเขต</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <!--begin::Menu sub-->
                        <div class="menu-sub menu-sub-dropdown w-175px py-4">
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="javascript:void(0)" class="menu-link px-3" id="auto_pin_marker">Automatic</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="javascript:void(0)" class="menu-link px-3" id="move_pin_label">ย้ายหมุด</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="javascript:void(0)" class="menu-link px-3" id="edit_pin_label">แก้ไขหมายเลขหมุด</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="javascript:void(0)" class="menu-link px-3" id="delete_pin_label">ลบ</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu separator-->
                            <!-- <div class="separator my-2"></div> -->
                            <!--end::Menu separator-->
                        </div>
                        <!--end::Menu sub-->
                    </div>
                    <!--end::Menu item-->
                    <!--begin::Menu item-->
                    <!-- <div class="menu-item px-3">
                        <a href="#" class="menu-link flex-stack px-3">กล่องข้อความ
                        <i class="fas fa-exclamation-circle ms-2 fs-7" data-bs-toggle="tooltip" title="" data-bs-original-title="Specify a target name for future usage and reference" aria-label="Specify a target name for future usage and reference"></i></a>
                    </div> -->
                    <!--end::Menu item-->
                    <!--begin::Menu item-->
                    <div class="menu-item px-3" data-kt-menu-trigger="hover" data-kt-menu-placement="right-end">
                        <a href="javascript:void(0)" class="menu-link px-3">
                            <span class="menu-title">วงหมุดเลขแปลง</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <!--begin::Menu sub-->
                        <div class="menu-sub menu-sub-dropdown w-175px py-4">
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="javascript:void(0)" class="menu-link px-3" id="add_no_parcel">เพิ่ม</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="javascript:void(0)" class="menu-link px-3" id="move_no_parcel">ย้าย</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="javascript:void(0)" class="menu-link px-3" id="del_no_parcel">ลบ</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu separator-->
                            <!-- <div class="separator my-2"></div> -->
                            <!--end::Menu separator-->

                        </div>
                        <!--end::Menu sub-->
                    </div>
                    <!--end::Menu item-->
                    <!--begin::Menu item-->
                    <div class="menu-item px-3" data-kt-menu-trigger="hover" data-kt-menu-placement="right-end">
                        <a href="javascript:void(0)" class="menu-link px-3">
                            <span class="menu-title">ข้อความ</span>
                            <span class="menu-arrow"></span>
                        </a>
                        <!--begin::Menu sub-->
                        <div class="menu-sub menu-sub-dropdown w-175px py-4">
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="javascript:void(0)" class="menu-link px-3" id="add_text_map" data-bs-toggle="modal" data-bs-target="#modal_text_map">เพิ่มข้อความ</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="javascript:void(0)" class="menu-link px-3" id="text_map_modal" >Properties</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="javascript:void(0)" class="menu-link px-3" id="text_move_menu">ย้าย</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="javascript:void(0)" class="menu-link px-3" id="text_copy_menu">คัดลอก</a>
                            </div>
                            <!--end::Menu item-->
                            <!--begin::Menu item-->
                            <div class="menu-item px-3">
                                <a href="javascript:void(0)" class="menu-link px-3" id="text_delete_menu">ลบ</a>
                            </div>
                            <!--end::Menu item-->

                            <!--begin::Menu separator-->
                            <!-- <div class="separator my-2"></div> -->
                            <!--end::Menu separator-->

                        </div>
                        <!--end::Menu sub-->
                    </div>
                    <!--end::Menu item-->
                    <!--begin::Menu item-->
                    <!-- <div class="menu-item px-3 my-1">
                        <a href="#" class="menu-link px-3" id="edif_shp_rent">แก้ไขรูปแปลง</a>
                    </div> -->
                    <!--end::Menu item-->
                    <!--begin::Menu item-->
                    <div class="menu-item px-3 my-1">
                        <a href="#" class="menu-link px-3" data-bs-toggle="modal" data-bs-target="#kt_modal_1">ข้อมูลกรรมสิทธิ์เช่า</a>
                    </div>
                    <!--end::Menu item-->
                </div>
                <!--end::Menu 3-->
            </div>
            <!--begin::Button-->
            <!-- <a href="{% url 'layout_page' %}" target="_blank" class="btn btn-primary fw-bold me-2" id="save">Save</a> -->
            <!--end::Button-->
            <!--begin::Button-->

            {% if id_time_line != 0 %}
            <a href="{% url 'layout_page' id_time_line %}" target="_blank" class="btn btn-primary fw-bold me-2" id="print_layout">Print Layout </a>
            {% endif %}

            <!-- <a href="{% url 'layout_page' id_time_line %}" target="_blank" class="btn btn-primary fw-bold me-2" id="print_layout">Print Layout </a> -->

            <!--end::Button-->
        </div>
        <!--end::Actions-->

    </div>
    <!--end::Container-->
</div>


<div id="kt_content_container" class="d-flex flex-column-fluid align-items-start  container-fluid ">
    <div class="col-xl-12 mb-5 mb-xl-10" >
									<!--begin::Chart widget 11-->
									<div class="card card-flush h-xl-100">
                                        <div id="map"></div>
									</div>
									<!--end::Chart widget 11-->
								</div>

</div>

<!-- model ข้อมูลกรรมสิทธิ์-->
<div class="modal fade" tabindex="-1" id="kt_modal_1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">ข้อมูลกรรมสิทธิ์</h5>

                <!--begin::Close-->
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"></span>
                </div>
                <!--end::Close-->
            </div>

            <div class="modal-body">
                <!--begin::Input group-->
                <div class="input-group mb-5">
                    <span class="input-group-text" id="inputGroup-sizing-default">ชื่อผู้เช่า</span>
                    <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" id="name_rent"/>
                </div>
                <!--end::Input group-->
                <!--begin::Input group-->
                <div class="input-group mb-5">
                    <span class="input-group-text" id="inputGroup-sizing-default">ประเภทสัญญา</span>
                    <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" id="prom_type"/>
                </div>
                <!--end::Input group-->
                <!--begin::Input group-->
                <div class="input-group mb-5">
                    <span class="input-group-text" id="inputGroup-sizing-default">ชื่ออัตรา</span>
                    <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" id="rate_type"/>
                </div>
                <!--end::Input group-->
                <!--begin::Input group-->
                <div class="input-group mb-5">
                    <span class="input-group-text" id="inputGroup-sizing-default">หมายเลขแปลง</span>
                    <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" id="number_parcel_rent"/>
                </div>
                <!--end::Input group-->
                <!--begin::Input group-->
                <div class="input-group mb-5">
                    <span class="input-group-text" id="inputGroup-sizing-default">ภาษาตำบล</span>
                    <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" id="talk_tunbon"/>
                </div>
                <!--end::Input group-->
                <!--begin::Input group-->
                <div class="input-group mb-5">
                    <span class="input-group-text" id="inputGroup-sizing-default">ตำบล</span>
                    <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" id="tumbon"/>
                </div>
                <!--end::Input group-->
                <!--begin::Input group-->
                <div class="input-group mb-5">
                    <span class="input-group-text" id="inputGroup-sizing-default">อำเภอ</span>
                    <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" id="amphoe"/>
                </div>
                <!--end::Input group-->
                <!--begin::Input group-->
                <div class="input-group mb-5">
                    <span class="input-group-text" id="inputGroup-sizing-default">จังหวัด</span>
                    <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" id="province"/>
                </div>
                <!--end::Input group-->
                <!--begin::Input group-->
                <div class="input-group mb-5">
                    <span class="input-group-text" id="inputGroup-sizing-default">โฉนดเลขที่</span>
                    <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" id="deed_no"/>
                </div>
                <!--end::Input group-->
                <!--begin::Input group-->
                <div class="input-group mb-5">
                    <span class="input-group-text" id="inputGroup-sizing-default">เลขที่ดิน</span>
                    <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" id="land_no"/>
                </div>
                <!--end::Input group-->
                <!--begin::Input group-->
                <div class="input-group mb-5">
                    <span class="input-group-text" id="inputGroup-sizing-default">พื้นที่เช่าประมาณ</span>
                    <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" id="area_rent_sqwa"/>
                </div>
                <!--end::Input group-->
                <!--begin::Input group-->
                <div class="input-group mb-5">
                    <span class="input-group-text" id="inputGroup-sizing-default">รังวัดวันที่</span>
                    <input type="text" class="form-control" aria-label="Sizing example input" aria-describedby="inputGroup-sizing-default" id="sv_datetime"/>
                </div>
                <!--end::Input group-->
            </div>

            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary">Save changes</button>
            </div>
        </div>
    </div>
</div>
<!-- end model ข้อมูลกรรมสิทธิ์ -->

<!--modal เพิ่มข้อความ-->
<div class="modal fade" tabindex="-1" id="modal_text_map">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">เพิ่มข้อความ</h5>

                <!--begin::Close-->
                <div class="btn btn-icon btn-sm btn-active-light-primary ms-2" data-bs-dismiss="modal" aria-label="Close">
                    <span class="svg-icon svg-icon-2x"></span>
                </div>
                <!--end::Close-->
            </div>

            <div class="modal-body">
                <!-- <p>Modal body text goes here.</p> -->
                <input type="text" id="input_text_map" class="form-control" placeholder="เพิ่มข้อความ"/>
                <!-- เว้นช่องว่าง -->
                <div class="mb-5"></div>
                <!-- add dropdown as id = dropdown_text_map -->
                <select class="form-select form-select-solid" data-control="select2" data-hide-search="true" data-placeholder="Select an option" id="dropdown_text_map">
                    <option value="text_map">แผนที่ฐาน</option>
                    <option value="building_text_map">สิ่งปลูกสร้าง</option>
                </select>
                <!-- end dropdown -->
            </div>

            


            <div class="modal-footer">
                <button type="button" class="btn btn-light" data-bs-dismiss="modal">Close</button>
                <button type="button" id="confirm_text" class="btn btn-primary" data-bs-dismiss="modal">Save changes</button>
            </div>
        </div>
    </div>
</div>
<!--end modal เพิ่มข้อความ-->




<!--begin::description_รายละเอียดโฉนดและสรุปข้อมูล drawer-->
<div
  id="text_map_detail"
  class="bg-body"
  data-kt-drawer="true"
  data-kt-drawer-name="description"
  data-kt-drawer-activate="true"
  data-kt-drawer-overlay="false"
  data-kt-drawer-width="{default:'350px', 'lg': '475px'}"
  data-kt-drawer-direction="end"
  data-kt-drawer-toggle="#text_map_modal"
  data-kt-drawer-close="#close_icon_text_map_detail"
>
  <!--begin::Card-->
  <div class="card shadow-none rounded-0 w-100">
    <!--begin::Header-->
    <div class="card-header" id="kt_engage_demos_header">
      <h3 class="card-title fw-bold text-gray-700">Properties</h3>
      <div class="card-toolbar">
        <button
          type="button"
          class="btn btn-sm btn-icon btn-active-color-primary h-40px w-40px me-n6"
          id="kt_engage_demos_close"
        >
          <!--begin::Svg Icon | path: icons/duotune/arrows/arr061.svg-->
          <span class="svg-icon svg-icon-2" id="close_icon_text_map_detail">
            <svg
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect
                opacity="0.5"
                x="6"
                y="17.3137"
                width="16"
                height="2"
                rx="1"
                transform="rotate(-45 6 17.3137)"
                fill="currentColor"
              />
              <rect
                x="7.41422"
                y="6"
                width="16"
                height="2"
                rx="1"
                transform="rotate(45 7.41422 6)"
                fill="currentColor"
              />
            </svg>
          </span>
          <!--end::Svg Icon-->
        </button>
      </div>
    </div>
    <!--end::Header-->
    <!--begin::Body-->
    <div class="card-body" id="kt_engage_demos_body">
      <!--begin::Content-->
      <div
        id="kt_explore_scroll"
        class="scroll-y me-n5 pe-5"
        data-kt-scroll="true"
        data-kt-scroll-height="auto"
        data-kt-scroll-wrappers="#kt_engage_demos_body"
        data-kt-scroll-dependencies="#kt_engage_demos_header"
        data-kt-scroll-offset="5px"
      >
        <!--begin::Wrapper-->
        <div class="mb-0">
          <!-- begin::Heading-->
          <!-- <div class="mb-7">
            <div class="d-flex flex-stack">
              <h3 class="mb-0">Text Properties</h3>
            </div>
          </div> -->
          <!--end::Heading -->

          <!--begin::Text Content-->
          <div class="mb-5">
            <label class="form-label fs-6 fw-bold text-gray-700">ข้อความ</label>
            <textarea class="form-control" id="id_text_map" rows="3" placeholder="Enter your text"></textarea>
          </div>
          <!--end::Text Content-->

          <!--begin::Font Size-->
          <div class="mb-5">
            <label class="form-label fs-6 fw-bold text-gray-700">Font Size (px)</label>
            <div class="d-flex align-items-center">
              <input type="range" class="form-range flex-grow-1 me-3" min="8" max="36" step="1" id="font_size_slider" value="16">
              <input type="number" class="form-control w-65px" id="font_size_input" value="16" min="8" max="36">
            </div>
          </div>
          <!--end::Font Size-->

          <!--begin::Rotation-->
          <div class="mb-5">
            <label class="form-label fs-6 fw-bold text-gray-700">Rotation (degrees)</label>
            <div class="d-flex align-items-center">
              <input type="range" class="form-range flex-grow-1 me-3" min="0" max="360" step="1" id="rotation_slider" value="0">
              <input type="number" class="form-control w-65px" id="rotation_input" value="0" min="0" max="360">
            </div>
          </div>
          <!--end::Rotation-->

          <!--begin::Actions-->
          <div class="d-flex justify-content-end">
            <button type="button" class="btn btn-sm btn-primary" id="apply_text_changes">
              Apply Changes
            </button>
          </div>
          <!--end::Actions-->

        </div>
        <!--end::Wrapper-->
      </div>
      <!--end::Content-->
    </div>
    <!--end::Body-->
  </div>
  <!--end::Card-->
</div>
<!--end::description_รายละเอียดโฉนดและสรุปข้อมูล drawer-->

{% endblock content_map %}

{% block js %}
<script src="https://unpkg.com/@geoman-io/leaflet-geoman-free@latest/dist/leaflet-geoman.js"></script>

<script>

    var layer_get_coordinate_point;
    proj4.defs('DOL INDIAN 1975 ZONE47N', "+proj=utm +zone=47 +ellps=evrst30 +towgs84=204.5,837.9,294.8,0,0,0,0 +units=m +no_defs +type=crs");
    proj4.defs('DOL INDIAN 1975 ZONE48N', "+proj=utm +zone=48 +ellps=evrst30 +towgs84=204.5,837.9,294.8,0,0,0,0 +units=m +no_defs +type=crs");
    proj4.defs('WGS84 UTM ZONE47N', "+title=WGS 1984 / UTM zone 47N+proj=utm +zone=47 +ellps=WGS84 +datum=WGS84 +units=m +no_defs");
    proj4.defs('WGS84 UTM ZONE48N', "+title=WGS 1984 / UTM zone 48N+proj=utm +zone=48 +ellps=WGS84 +datum=WGS84 +units=m +no_defs");
    proj4.defs('WGS84', "+title=longlat / WGS84+proj=longlat +datum=WGS84 +no_defs +units=degrees");

    // http://localhost:8001/editor_page/130/ split by /
    var url = window.location.href;
    var urlSplit = url.split('/');
    var id = urlSplit[urlSplit.length - 2];
    // it to float
    id_parcel_rent = parseFloat(id);
    console.log(id);

    $(document).ready(function () {
        var wkt = new Wkt.Wkt();

        var fontBase64 = 'data:application/font-woff;base64,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';
        // BASE MAP
        const osm = L.tileLayer(
        "https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png",
        {
            maxNativeZoom: 200,
            maxZoom: 200,
            attribution:
            "Map data © <a href='https://openstreetmap.org'>OpenStreetMap</a> contributors",
        }
        );

        const ggl3 = L.tileLayer(
        "https://mt1.google.com/vt/lyrs=y&x={x}&y={y}&z={z}",
        {
            maxNativeZoom: 200,
            maxZoom: 200,
            attribution: '&copy; <a href="#">Google Map Hybrid</a>',
        }
        );
        const blank = L.tileLayer("", {
            maxNativeZoom: 200,
            maxZoom: 200,
        });
        const map = L.map("map", {
        // zoomDelta: 0.1, // ความละเอียดการ zoom
        zoomSnap: 0.1, // ความละเอียดการ zoom
        // zoomDelta: 0.25, // ความละเอียดการ zoom
        // zoomSnap: 0, // ความละเอียดการ zoom
        // remove Zoom Control
        zoomControl: false,
        // scrollWheelZoom: false, // ปิดการซูมด้วยล้อเลื่อนของเมาส์
        // doubleClickZoom: false, // ปิดการซูมด้วยการดับเบิ้ลคลิก
        // touchZoom: false, // ปิดการซูมด้วยการสัมผัส
        // layer
        layers: [blank],
        }).setView([13.680084, 100.539492], 9);

        // ปิดการแสดง Leaflet ที่มุมล่างขวา
        map.attributionControl.setPrefix(false);

        baseMaps = {
            "OSM": osm,
            "Google Map Hybrid": ggl3,
            "Blank": blank,
        };
        overlayMaps = {};

        // เพิ่ม Layer Control มุมด้านขวาบนชั้น layer option คือ collapsed:true
        ctl_lry = {
            // show layer control
            collapsed: false,
            position: 'topleft'
        }
        layerControl = L.control.layers(baseMaps, overlayMaps, ctl_lry).addTo(map);

        // show id rent_parcel_preview
        var odm_3d = L.control({ position: "bottomright" });
        odm_3d.onAdd = function (map) {
        // show button vertical
        var div = L.DomUtil.create("div", "odm_3d");
        // add icon
        div.innerHTML += '<div class="btn-group" role="group" aria-label="Basic example">'+
                    '<select id="scale_select" name="scale_select">'+
                        '<option value="1">Select Scale</option>'+
                        '<option value="100">1:100</option>'+
                        '<option value="150">1:150</option>'+
                        '<option value="200">1:200</option>'+
                        '<option value="250">1:250</option>'+
                        '<option value="300">1:300</option>'+
                        '<option value="350">1:350</option>'+
                        '<option value="400">1:400</option>'+
                        '<option value="450">1:450</option>'+
                        '<option value="500">1:500</option>'+
                        '<option value="550">1:550</option>'+
                        '<option value="600">1:600</option>'+
                        '<option value="650">1:650</option>'+
                        '<option value="700">1:700</option>'+
                        '<option value="750">1:750</option>'+
                        '<option value="800">1:800</option>'+
                        '<option value="850">1:850</option>'+
                        '<option value="900">1:900</option>'+
                        '<option value="950">1:950</option>'+
                        '<option value="1000">1:1000</option>'+
                        '<option value="1250">1:1250</option>'+
                        '<option value="2000">1:2000</option>'+
                        '<option value="2500">1:2500</option>'+
                        '<option value="3000">1:3000</option>'+
                        '<option value="3500">1:3500</option>'+
                        '<option value="4000">1:4000</option>'+
                        '<option value="4500">1:4500</option>'+
                        '<option value="5000">1:5000</option>'+
                        '<option value="5500">1:5500</option>'+
                        '<option value="6000">1:6000</option>'+
                        '</select>'+
                    '</div>';
        div.innerHTML += '&nbsp;';
        div.innerHTML += '<button type="button" class="btn btn-sm btn-primary" id="btn_left"> <i class="fas fa-arrow-left"></i> </button>';
        // add space
        div.innerHTML += '&nbsp;';
        // add icon
        div.innerHTML += '<button type="button" class="btn btn-sm btn-primary" id="btn_3d">คัดลอก</button>';
        // add space
        div.innerHTML += '&nbsp;';
        // add icon
        div.innerHTML += '<button type="button" class="btn btn-sm btn-primary" id="cancle_all">ยกเลิก</button>';

        return div;
        };
        odm_3d.addTo(map);

        // add icon left
        // show id rent_parcel_preview
        var button_left = L.control({ position: "bottomleft" });
        button_left.onAdd = function (map) {
        // show button vertical
        var div_left = L.DomUtil.create("div", "button_left");
        // add input as textare
        div_left.innerHTML += '<textarea class="form-control" rows="3" id="edit_pin_label_textarea" style="display: none; width: 200px;  font-family: Sarabun; font-size: 16px;">';
        // add space
        div_left.innerHTML += '&nbsp;';
        // add input as textare
        return div_left;
        };
        button_left.addTo(map);
        // End add icon left



        {% if id_time_line != 0 %}
        var id_time_line = {{ id_time_line }};
        // parseFloat
        id_time_line = parseFloat(id_time_line);
        console.log(id_time_line);
        {% endif %}


        // ตั้งค่าความสูง Layer การแสดงผล
        map.createPane('pane_parcel_rent').style.zIndex = 100; // แปลงเช่า
        map.createPane('pane_side_parcel').style.zIndex = 150; // แปลงข้างเคียง
        map.createPane('pane_building_on_parcel').style.zIndex = 200; // สิ่งปลูกสร้าง
        map.createPane('pane_side_plot_lines').style.zIndex = 250; // ขีดเขตแยก
        map.createPane('pane_polygon_scale').style.zIndex = 90; // กรอบ scale

        // stype polygon
        var polygonStyle_rent = {
            "color": "#000000", // black color
            "weight": 2,
            "opacity": 1,
            "fillOpacity": 0, // Adjust fillOpacity as needed
        };
        var number_parcel_rent;
        // แสดงข้อมูลกรรมสิทธิ์เช่า
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/show_asset_rent/',
            data: {
                'id_parcel_rent': id_parcel_rent,
                'name_rent': 'name_rent',
                'prom_type': 'prom_type',
                'rate_type': 'rate_type',
                'number_parcel_rent': 'number_parcel_rent',
                'talk_tunbon': 'talk_tunbon',
                'tumbon': 'tumbon',
                'amphoe': 'amphoe',
                'province': 'province',
                'deed_no': 'deed_no',
                'land_no': 'land_no',
                'area_rent_sqwa': 'area_rent_sqwa',
                'sv_datetime': 'sv_datetime',
            },
            type: 'POST',
            success: function (response) {
                console.log(response);
                // ชื่อผู้เช่า
                $('#name_rent').val(response.name_rent);
                // ประเภทสัญญา
                $('#prom_type').val(response.prom_type);
                // ชื่ออัตรา
                $('#rate_type').val(response.rate_type);
                // หมายเลขแปลง
                $('#number_parcel_rent').val(response.number_parcel_rent);
                number_parcel_rent = response.number_parcel_rent;
                // ภาษาตำบล
                $('#talk_tunbon').val(response.talk_tunbon);
                // ตำบล
                $('#tumbon').val(response.tumbon);
                // อำเภอ
                $('#amphoe').val(response.amphoe);
                // จังหวัด
                $('#province').val(response.province);
                // โฉนดเลขที่
                $('#deed_no').val(response.deed_no);
                // เลขที่ดิน
                $('#land_no').val(response.land_no);
                // พื้นที่เช่าประมาณ
                $('#area_rent_sqwa').val(response.area_rent_sqwa);
                // รังวัดวันที่
                $('#sv_datetime').val(response.sv_datetime);
            }
        });

        // เพิ่ม หรือ อัพเดท ข้อมูลกรรมสิทธิ์เช่า
        $('#kt_modal_1 .btn-primary').click(function () {
            // ชื่อผู้เช่า
            var name_rent = $('#name_rent').val();
            // ประเภทสัญญา
            var prom_type = $('#prom_type').val();
            // ชื่ออัตรา
            var rate_type = $('#rate_type').val();
            // หมายเลขแปลง
            var number_parcel_rent = $('#number_parcel_rent').val();
            // ภาษาตำบล
            var talk_tunbon = $('#talk_tunbon').val();
            // ตำบล
            var tumbon = $('#tumbon').val();
            // อำเภอ
            var amphoe = $('#amphoe').val();
            // จังหวัด
            var province = $('#province').val();
            // โฉนดเลขที่
            var deed_no = $('#deed_no').val();
            // เลขที่ดิน
            var land_no = $('#land_no').val();
            // พื้นที่เช่าประมาณ
            var area_rent_sqwa = $('#area_rent_sqwa').val();
            // รังวัดวันที่
            var sv_datetime = $('#sv_datetime').val();
            console.log(name_rent);


            // แก้ไข หรือ อัพเดท ข้อมูลกรรมสิทธิ์เช่า
            $.ajax({
                // add csrf token
                headers: { "X-CSRFToken": "{{ csrf_token }}" },
                url: '/add_asset_rent/',
                data: {
                    'id_parcel_rent': id_parcel_rent,
                    'name_rent': name_rent,
                    'prom_type': prom_type,
                    'rate_type': rate_type,
                    'number_parcel_rent': number_parcel_rent,
                    'talk_tunbon': talk_tunbon,
                    'tumbon': tumbon,
                    'amphoe': amphoe,
                    'province': province,
                    'deed_no': deed_no,
                    'land_no': land_no,
                    'area_rent_sqwa': area_rent_sqwa,
                    'sv_datetime': sv_datetime,
                },
                type: 'POST',
                success: function (response) {
                    console.log(response);
                    // close modal
                    $('#kt_modal_1').modal('hide');
                }
            });
        });

        var shp_rent;
        // ajax แสดงรูปแปลงที่ดิน
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/ajax_parcel_rent/',
            data: {
                'id_parcel_rent': id_parcel_rent,
            },
            type: 'POST',
            success: function (response) {
                // console.log(response);
                shp_rent = L.geoJson(response, {
                    pane: 'pane_parcel_rent',
                    style: polygonStyle_rent,
                    onEachFeature: function (feature, layer) {
                        // layer.bindPopup(feature.properties.id);
                        // Calculate the centroid of the original feature
                        var center = turf.centroid(feature);
                        var center_geojson = center.geometry.coordinates;

                    }
                });
                // add layer to group
                shp_rent_group = L.layerGroup([shp_rent]);
                // add layer to layer control checked box
                layerControl.addOverlay(shp_rent_group, "แปลงที่ดิน");
                // add layer to map
                shp_rent_group.addTo(map);
            }
        });
        // End ajax แสดงรูปแปลงที่ดิน

        // stype polygon
        var polygonStyle_bu = {
            "color": "#FE0202", // black color
            "weight": 1,
            "opacity": 1,
            "fillOpacity": 0, // Adjust fillOpacity as needed
        };
        // ajax แสดงสิ่งปลูกสร้าง
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/ajax_building_on_parcel/',
            data: {
                'id_parcel_rent': id_parcel_rent,
            },
            type: 'POST',
            success: function (response) {
                // console.log(response);
                shp_bu = L.geoJson(response, {
                    pane: 'pane_building_on_parcel',
                    style: polygonStyle_bu,
                    onEachFeature: function (feature, layer) {
                        layer.bindPopup(feature.properties.id);
                    }
                });
                // add layer to group
                shp_bu_group = L.layerGroup([shp_bu]);
                // add layer to layer control checked box
                layerControl.addOverlay(shp_bu_group, "สิ่งปลูกสร้าง");
                // add layer to map
                shp_bu_group.addTo(map);
                // bringToBack
                shp_bu_group.bringToBack();

            }
        });
        // End ajax แสดงสิ่งปลูกสร้าง

        // stype polygon
        var polygonStyle_side_parcel = {
            "color": "#D0D0D1", // black color
            "weight": 1,
            "opacity": 1,
            "fillOpacity": 0, // Adjust fillOpacity as needed
        };

        // ajax แสดงรูปแปลงข้างเคียง
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/ajax_side_parcel/',
            data: {
                'id_parcel_rent': id_parcel_rent,
                'offset_distance': 20, // ระยะห่างของรูปแปลงข้างเคียง
            },
            type: 'POST',
            success: function (response) {
                console.log(response);
                shp_side_parcel = L.geoJson(response, {
                    pane: 'pane_side_parcel',
                    style: polygonStyle_side_parcel,

                });
                // add layer to group
                shp_side_parcel_group = L.layerGroup([shp_side_parcel]);
                // add layer to layer control checked box
                layerControl.addOverlay(shp_side_parcel_group, "รูปแปลงข้างเคียง");
                // add layer to map
                shp_side_parcel_group.addTo(map);
                // fitbounds
                // map.fitBounds(shp_side_parcel.getBounds());
            }
        });


        // Ajax แสดงข้อมูล วงหมุดหมายเลขแปลง
        // Create a new pane for top-most layer rendering
        var topPane = map.createPane('pane_circle_parcel'); // วงหมุดหมายเลขแปลง
        topPane.style.zIndex = 651;  // Higher than the overlay pane which is 400 by default
        // ajax
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/ajax_parcel_number/',
            data: {
                'id_time_line': id_time_line,
                'type': 'no_parcel',
                'type_query': 'get',
                'scale_map': scale,
            },
            type: 'POST',
            success: function (response) {
                // console.log(response);
                // get geojson
                var geojson = response;
                // add geojson to map
                L.geoJson(geojson, {
                    onEachFeature: function (feature, layer) {
                        // pane
                        layer.options.pane = 'pane_circle_parcel';
                        // add property to layer
                        layer.properties = feature.properties;
                        console.log(layer.properties);
                        if (layer.properties.type =='no_parcel') {
                            var svgContent = `
                            <svg height="100" width="100" xmlns="http://www.w3.org/2000/svg">
                                <style>
                                    @font-face {
                                        font-family: 'THSarabun';
                                        src: url(${fontBase64}) format('woff');
                                    }
                                    text {
                                        font-family: 'THSarabun';
                                        font-size: 40px;
                                        fill: black;
                                        dominant-baseline: middle;
                                        text-anchor: middle;
                                        
                                    }
                                </style>
                                <circle r="45" cx="50" cy="50" fill="white" stroke="black" stroke-width="3" />
                                <text x="50%" y="50%">${layer.properties.content}</text>
                            </svg>`;
                            // Encode SVG content as base64
                            var svgDataUri = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgContent)));
                            // Define custom icon using the SVG Data URI
                            var myIcon = L.icon({
                                iconUrl: svgDataUri,
                                iconSize: [60, 60], // Size of the icon
                                iconAnchor: [30, 30], // Center of the icon
                                popupAnchor: [0, -30] // Popup anchor point
                            });
                            // add to map
                            layer.setIcon(myIcon);
                            // Add the line to the layer group
                            circle_parcel_layer.addLayer(layer);
                        }
                        if (layer.properties.type =='text_map') {
                    var textIcon = L.divIcon({
                        className: 'text_map2mm',
                        html: '<div style="font-size: 16px; transform: rotate(0deg); line-height: 14px;">' +  + '</div>',
                        iconSize: [100, 30]
                    });
                    // สร้าง marker ด้วย text icon
                    var textMarker = L.marker(latlng, {
                        icon: textIcon,
                        pane: 'pane_text_map'
                    });
                    // เพิ่ม marker ลงใน text_map_group
                    text_map_group.addLayer(textMarker);
                }
                    }

                });


            }
        });

        // End Ajax แสดงข้อมูล วงหมุดหมายเลขแปลง


        // Define a layer group for side plot lines
        var pin_marker_rents = L.layerGroup().addTo(map);
        // Define a layer group for side plot lines
        var pin_marker_rents_label = L.layerGroup().addTo(map);

        // add layer to layer control checked box
        layerControl.addOverlay(pin_marker_rents, "หมุดหลักเขต");
        // add layer to layer control checked box
        layerControl.addOverlay(pin_marker_rents_label, "ตัวหนังสือหมุดหลักเขต");
        // Create a new pane for top-most layer rendering

        var topPane = map.createPane('pane_pin_boundary_rent'); // หมุดหลักเขต
        topPane.style.zIndex = 651;  // Higher than the overlay pane which is 400 by default

        // Define custom icon
        var url_icon = '/static/assets/svg/point.svg';
        var myIcon = L.icon({
            iconUrl: url_icon,
            iconSize: [15, 15], // ขนาดของไอคอน
            iconAnchor: [7.5, 7.5], // จุดกึ่งกลางไอคอนจะอยู่ที่จุดกึ่งกลางของไอคอน (ครึ่งของ 18)
            popupAnchor: [0, -9] // ปรับป็อปอัพให้อยู่ด้านบนของหมุด
        });
        // โหลดหมุดเขต
        $.ajax({
            headers: { "X-CSRFToken": "{{ csrf_token }}" }, // Ensure CSRF token is sent with request
            url: '/ajax_pin_rents/', // URL to the Django view
            data: {
                'id_time_line': id_time_line, // Variable id_time_line should be defined in your script
                'type_query': 'get_pin_rents',
            },
            type: 'POST',
            success: function (response) {
                var geojsonLayer = L.geoJson(response['point_rents'], {
                        pointToLayer: function(feature, latlng) {
                            return L.marker(latlng, {icon: myIcon, pane: 'pane_pin_boundary_rent'}); // Use the top pane
                        },
                        onEachFeature: function (feature, layer) {
                            // add property to layer
                            layer.properties = feature.properties;
                            // Add layer to layer group
                            pin_marker_rents.addLayer(layer);
                            // Add layer to map
                            layer.addTo(map);
                        }
                    });
                // Create a GeoJSON layer from the response with custom icon for each marker
                var geojsonLayer_label = L.geoJson(response['point_label'], {
                        onEachFeature: function (feature, layer) {
                            // split by \n
                            var contents = feature.properties.contents.split('\n');
                            // count contents
                            var count_contents = contents.length;
                            if (count_contents > 1) {
                                // ข้อความ 2 บรรทัด
                                var textIcon = L.divIcon({
                                    className: 'text_map2mm',
                                    html: '<div style="transform: rotate(0deg);">' + contents[0] + '<br>' + contents[1] + '</div>',
                                    iconSize: [100, 40] // Optional, depending on the need to adjust the icon size
                                });
                            } else {
                                // ข้อความ 1 บรรทัด
                                var textIcon = L.divIcon({
                                    className: 'text_map2mm',
                                    html: '<div style="transform: rotate(0deg);">' + feature.properties.contents + '</div>',
                                    iconSize: [100, 30] // Optional, depending on the need to adjust the icon size
                                });
                            }

                            // Set the custom icon to the layer
                            if (layer instanceof L.Marker) {
                                layer.setIcon(textIcon);
                                layer.options.pane = 'pane_pin_boundary_rent';
                            } else if (layer instanceof L.Path) {
                                // Optional: Handle cases where the layer is not a marker but another type like polygons
                                // This is just in case your GeoJSON contains other types of geometries
                            }

                            // Add the feature's properties to the layer
                            layer.properties = feature.properties;

                            // Add layer to layer group and map
                            pin_marker_rents_label.addLayer(layer);
                            layer.addTo(map);
                        }
                    });
            }
        });
        // End โหลดหมุดเขต

        // เพิ่มหมุดเขตที่ดิน Automatic
        $('#auto_pin_marker').click(function() {
            console.log('auto_pin_marker');

            // Define custom icon
            var url_icon = '/static/assets/svg/point.svg';
            var myIcon = L.icon({
                iconUrl: url_icon,
                iconSize: [15, 15], // ขนาดของไอคอน
                iconAnchor: [7.5, 7.5], // จุดกึ่งกลางไอคอนจะอยู่ที่จุดกึ่งกลางของไอคอน (ครึ่งของ 18)
                popupAnchor: [0, -9] // ปรับป็อปอัพให้อยู่ด้านบนของหมุด
            });




            // Create a new pane for top-most layer rendering
            var topPane = map.createPane('pane_pin_boundary_rent'); // หมุดหลักเขต
            topPane.style.zIndex = 651;  // Higher than the overlay pane which is 400 by default

            // Ajax call to get GeoJSON data
            $.ajax({
                headers: { "X-CSRFToken": "{{ csrf_token }}" }, // Ensure CSRF token is sent with request
                url: '/ajax_pin_rents/', // URL to the Django view
                data: {
                    'id_time_line': id_time_line, // Variable id_time_line should be defined in your script
                    'type_query': 'get_vertex',
                },
                type: 'POST',
                success: function (response) {


                    // Create a GeoJSON layer from the response with custom icon for each marker
                    var geojsonLayer = L.geoJson(response['point_rents'], {
                        pointToLayer: function(feature, latlng) {
                            return L.marker(latlng, {icon: myIcon, pane: 'pane_pin_boundary_rent'}); // Use the top pane
                        },
                        onEachFeature: function (feature, layer) {
                            // add property to layer
                            layer.properties = feature.properties;
                            // Add layer to layer group
                            pin_marker_rents.addLayer(layer);
                            // Add layer to map
                            layer.addTo(map);
                        }
                    });
                    console.log(response['point_label']); // Log the response to the console
                    // Create a GeoJSON layer from the response with custom icon for each marker
                    var geojsonLayer_label = L.geoJson(response['point_label'], {
                        onEachFeature: function (feature, layer) {
                            // split by \n
                            var contents = feature.properties.contents.split('\n');
                            // count contents
                            var count_contents = contents.length;
                            if (count_contents > 1) {
                                // ข้อความ 2 บรรทัด
                                var textIcon = L.divIcon({
                                    className: 'text_map2mm',
                                    html: '<div style="transform: rotate(0deg);">' + contents[0] + '<br>' + contents[1] + '</div>',
                                    iconSize: [100, 40] // Optional, depending on the need to adjust the icon size
                                });
                            } else {
                                // ข้อความ 1 บรรทัด
                                var textIcon = L.divIcon({
                                    className: 'text_map2mm',
                                    html: '<div style="transform: rotate(0deg);">' + contents[0] + '</div>',
                                    iconSize: [100, 30] // Optional, depending on the need to adjust the icon size
                                });
                            }

                            // Set the custom icon to the layer
                            if (layer instanceof L.Marker) {
                                // seticon and pane
                                layer.setIcon(textIcon);
                                layer.options.pane = 'pane_pin_boundary_rent';
                            } else if (layer instanceof L.Path) {
                                // Optional: Handle cases where the layer is not a marker but another type like polygons
                                // This is just in case your GeoJSON contains other types of geometries
                            }

                            // Add the feature's properties to the layer
                            layer.properties = feature.properties;

                            // Add layer to layer group and map
                            pin_marker_rents_label.addLayer(layer);
                            layer.addTo(map);
                        }
                    });
                }
            });



        });
        // End เพิ่มหมุดเขตที่ดิน Automatic


        // เพิ่มหมุดเขตที่ดิน Manual
        pin_boundary = [];
        $('#click_pin_marker').click(function () {
            // close modal
            $('#modal_add_pin_marker').modal('hide');

            // add control draw polyline only
            map.pm.addControls({
                position: 'topright',
                drawPolyline: false,
                drawCircleMarker: false,
                rotateMode: false,
                drawMarker: true,
                drawCircle: false,
                drawRectangle: false,
                drawPolygon: false,
                drawText: false,
                editControls : false,

            });
            // add pathoptions
            map.pm.setPathOptions({
                color: '#000000',
                weight: 1,
                opacity: 1,
            });

            // change icon
            var url_icon = '/static/assets/svg/point.svg';
            var myIcon = L.icon({
                iconUrl: url_icon,
                iconSize: [15.5, 15.5], // ขนาดของไอคอน
                iconAnchor: [7.5, 7.5], // จุดกึ่งกลางไอคอนจะอยู่ที่จุดกึ่งกลางของไอคอน (ครึ่งของ 18)
                popupAnchor: [0, -9] // ปรับป็อปอัพให้อยู่ด้านบนของหมุด
            });


            map.on('pm:create', function (e) {

                // // change icon
                // var pin = e.marker.setIcon(L.divIcon({
                //     className: 'my-div-icon',
                //     html: '<svg viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" fill="#FFFFFF" stroke="#000000" stroke-width="1" stroke-linecap="round" stroke-linejoin="miter"><circle cx="12" cy="12" r="10"></circle><circle cx="12" cy="12" r="1" fill="#000000"></circle></svg>',
                // }));

                url_icon = '/static/assets/svg/point.svg';
                // console.log(url_icon);
                var myIcon = L.icon({
                    iconUrl: url_icon,
                    iconSize: [15.5, 15.5], // ขนาดของไอคอน
                    iconAnchor: [7.5, 7.5], // จุดกึ่งกลางไอคอนจะอยู่ที่จุดกึ่งกลางของไอคอน (ครึ่งของ 18)
                    popupAnchor: [0, -9] // ปรับป็อปอัพให้อยู่ด้านบนของหมุด
                });

                // create marker
                var pin = e.marker.setIcon(myIcon);



                // Enable dragging for this pin
                pin.options.draggable = true;
                pin.dragging.enable(); // This line makes the marker draggable

                // snap to layer
                pin.pm.enable({
                    snappable: true,
                    snapDistance: 20,
                    snapSegment: true,
                    cursorMarker: true,
                });
                // add the pin to the map
                pin.addTo(map);

                console.log(e);

                // get latlng
                var latlng = e.marker.getLatLng();

                // add text
                // text_boundary_rent(map, latlng, 'ส ๖๕๔๓');

                // disable Draw Mode
                map.pm.disableDraw('Marker');

                // add to pin_marker_rents
                pin_marker_rents.addLayer(pin);
            });
            // pane

        });
        // End หมุดเขตที่ดิน Manual


        // ย้ายตัวหนังสือ หมุดเขต
        $('#move_pin_label').click(function () {
            console.log('move_pin_label');
            // If there are lines added, enable editing for those
            if (pin_marker_rents_label.getLayers().length > 0) {
                // Disable global edit mode to ensure we're only editing the target layer group
                map.pm.disableGlobalEditMode();
                // Enable edit mode for each line in the layer group
                pin_marker_rents_label.eachLayer(function(layer) {
                    layer.pm.enable({
                        snappable: false,
                    });
                    // listen to when a layer is changed in Edit Mode
                    layer.on("pm:edit", (e) => {
                        // get properties id
                        var id = layer.properties.id;
                        // get contents
                        var contents = layer.properties.contents;
                        // get latlng
                        var latlng = layer.getLatLng();
                        // console.log(id);
                        // add text
                        // text_boundary_rent(map, latlng, id);
                        // ajax edit pin label
                        $.ajax({
                            // add csrf token
                            headers: { "X-CSRFToken": "{{ csrf_token }}" },
                            url: '/ajax_pin_rents/',
                            data: {
                                'id_time_line': id_time_line,
                                'id_pin_label': id,
                                'type_query': 'edit_pin_label',
                                'lat': latlng.lat,
                                'lng': latlng.lng,
                                'contents': contents,
                            },
                            type: 'POST',
                            success: function (response) {
                                console.log(response);
                            }
                        });
                    });
                });
            } else {
                console.log('No lines to edit.');
            }
        });

        // End ย้ายตัวหนังสือ หมุดเขต

        // แก้ไข หมายเลขหมุดเขต
        $('#edit_pin_label').click(function () {
            var id;
            var latlng;
            console.log('edit_pin_label');
            // If there are lines added, enable editing for those
            if (pin_marker_rents_label.getLayers().length > 0) {
                // Disable global edit mode to ensure we're only editing the target layer group
                map.pm.disableGlobalEditMode();
                // Enable edit mode for each line in the layer group
                pin_marker_rents_label.eachLayer(function(layer) {
                    layer.pm.enable({
                        snappable: false,
                    });

                    // show textarea edit_pin_label
                    $('#edit_pin_label_textarea').show();
                    // click layer
                    layer.on('click', function(e) {
                        // get properties id
                        id = layer.properties.id;
                        // get contents
                        var contents = layer.properties.contents;
                        console.log(contents);
                        // set value to textarea
                        $('#edit_pin_label_textarea').val(contents);
                        // get latlng
                        latlng = layer.getLatLng();

                    });

                });
            } else {
                console.log('No lines to edit.');
            }
            // when change textarea
            $('#edit_pin_label_textarea').change(function() {
                // get value textarea
                var contents = $('#edit_pin_label_textarea').val();
                // ajax edit pin label
                $.ajax({
                    // add csrf token
                    headers: { "X-CSRFToken": "{{ csrf_token }}" },
                    url: '/ajax_pin_rents/',
                    data: {
                        'id_time_line': id_time_line,
                        'id_pin_label': id,
                        'type_query': 'edit_pin_label',
                        'lat': latlng.lat,
                        'lng': latlng.lng,
                        'contents': contents,
                    },
                    type: 'POST',
                    success: function (response) {
                        console.log(response);

                        // Properly splitting by '\n'
                        var splitContents = contents.split('\n');
                        console.log(splitContents);
                        // change contents
                        pin_marker_rents_label.eachLayer(function(layer) {
                            if (layer.properties.id == id) {
                                // add property to layer
                                layer.properties.contents = contents;
                                // count contents
                                var count_contents = splitContents.length;
                                if (count_contents > 1) {
                                    // ข้อความ 2 บรรทัด
                                    var textIcon = L.divIcon({
                                        className: 'text_map1mm',
                                        html: '<div style="transform: rotate(0deg);">' + splitContents[0] + '<br>' + splitContents[1] + '</div>',
                                        iconSize: [100, 40] // Optional, depending on the need to adjust the icon size
                                    });
                                } else {
                                    // ข้อความ 1 บรรทัด
                                    var textIcon = L.divIcon({
                                        className: 'text_map1mm',
                                        html: '<div style="transform: rotate(0deg);">' + splitContents[0] + '</div>',
                                        iconSize: [100, 30] // Optional, depending on the need to adjust the icon size
                                    });
                                }
                                // Set the custom icon to the layer
                                if (layer instanceof L.Marker) {
                                    layer.setIcon(textIcon);
                                    layer.options.pane = 'pane_pin_boundary_rent';
                                } else if (layer instanceof L.Path) {
                                    // Optional: Handle cases where the layer is not a marker but another type like polygons
                                    // This is just in case your GeoJSON contains other types of geometries
                                }

                            }
                        });

                    }
                });
            });
        });
        // END แก้ไข หมายเลขหมุดเขต

        // ลบ หมุดเขต
        $('#delete_pin_label').click(function () {
            console.log('delete_pin_label');
            // If there are lines added, enable editing for those
            if (pin_marker_rents_label.getLayers().length > 0) {
                // Disable global edit mode to ensure we're only editing the target layer group
                map.pm.disableGlobalEditMode();

                // enable removal mode like this:
                map.pm.enableGlobalRemovalMode();
                // listen to when a layer is removed
                map.on('pm:remove', function(e) {
                    var layer = e.layer;
                    // get properties id
                    var id = e.layer.properties.id;
                    // console.log properties type
                    console.log(e.layer);
                    // console.log(id);
                    // ajax delete pin label
                    $.ajax({
                        // add csrf token
                        headers: { "X-CSRFToken": "{{ csrf_token }}" },
                        url: '/ajax_pin_rents/',
                        data: {
                            'id_time_line': id_time_line,
                            'id_pin_label': id,
                            'type_query': 'delete_pin_label',
                        },
                        type: 'POST',
                        success: function (response) {
                            // console.log(response);
                            // remove layer property id on layer pin_marker_rents
                            pin_marker_rents_label.eachLayer(function(layer) {
                                if (layer.properties.id == id) {
                                    pin_marker_rents_label.removeLayer(layer);
                                }
                            });
                            pin_marker_rents.eachLayer(function(layer) {
                                if (layer.properties.id == response) {
                                    pin_marker_rents.removeLayer(layer);
                                }
                            });
                        }
                    });
                });

            } else {
                console.log('No lines to edit.');
            }
        });
        // End ลบ หมุดเขต

        // Define a layer group for side plot lines
        var sidePlotLinesLayerGroup = L.layerGroup().addTo(map);
        // add layer to layer control checked box
        layerControl.addOverlay(sidePlotLinesLayerGroup, "ขีดเขตแยก");

        // เพิ่ม ขีดเขตแยก
        $('#add_side_plot_lines').click(function () {
            // try remove
            try {
                map.pm.removeControls();
            } catch (error) {
                console.log(error);
            }
            // add control draw polyline only
            map.pm.addControls({
                position: 'topright',
                drawPolyline: true,
                drawCircleMarker: false,
                rotateMode: false,
                drawMarker: false,
                drawCircle: false,
                drawRectangle: false,
                drawPolygon: false,
                drawText: false,
                editControls : false,

            });
            // add pathoptions
            map.pm.setPathOptions({
                color: '#000000',
                weight: 1,
                opacity: 1,
            });


            // check if the layer is a polyline
            map.on('pm:create', function(e) {
                // console.log(e);
                // check if the layer is a LineString
                if (e.layer instanceof L.Polyline) {
                    // get selected scale
                    var scale_map = $('#scale_select').val();




                    // Add the line to the layer group
                    sidePlotLinesLayerGroup.addLayer(e.layer);
                    // geometry to wicket
                    wkt.fromObject(e.layer);
                    // get wkt
                    var wktString = wkt.write();
                    // console.log(wktString);
                    // remove layer
                    e.layer.remove();
                    $.ajax({
                        // add csrf token
                        headers: { "X-CSRFToken": "{{ csrf_token }}" },
                        url: '/side_plot_lines/',
                        data: {
                            'id_time_line': id_time_line,
                            'type_query': 'add',
                            'wktString': wktString,

                        },
                        type: 'POST',
                        success: function (response) {
                            console.log(response);
                            // add geojson to sidePlotLinesLayerGroup add pane_side_parcel
                            L.geoJson(response, {
                                pane: 'pane_side_plot_lines',
                                style: polygonStyle_side_parcel,
                                onEachFeature: function (feature, layer) {
                                    layer.properties = feature.properties;
                                    // Add the line to the layer group
                                    sidePlotLinesLayerGroup.addLayer(layer);
                                }
                            });
                            // change style
                            sidePlotLinesLayerGroup.eachLayer(function(layer) {
                                layer.setStyle({
                                    color: '#000000',
                                    weight: 1,
                                    opacity: 1,
                                });
                            });
                        }
                    });
                }

            });




        });
        // End เพิ่ม ขีดเขตแยก

        // แก้ไข ขีดเขตแยก
        $('#edit_side_plot_lines').click(function () {
            console.log('Edit');
            // If there are lines added, enable editing for those
            if (sidePlotLinesLayerGroup.getLayers().length > 0) {
                // Disable global edit mode to ensure we're only editing the target layer group
                map.pm.disableGlobalEditMode();

                // Enable edit mode for each line in the layer group
                sidePlotLinesLayerGroup.eachLayer(function(layer) {
                    layer.pm.enable({
                        snappable: true,
                        snapDistance: 20,
                        snapSegment: true,
                        cursorMarker: true,
                    });
                    // listen to when a layer is changed in Edit Mode
                    layer.on("pm:edit", (e) => {
                        // get properties id
                        var id = layer.properties.id;
                        // get wkt
                        wkt.fromObject(layer);
                        // get wkt
                        var wktString = wkt.write();
                        // ajax edit line side_plot_lines
                        $.ajax({
                            // add csrf token
                            headers: { "X-CSRFToken": "{{ csrf_token }}" },
                            url: '/side_plot_lines/',
                            data: {
                                'id_time_line': id_time_line,
                                'type_query': 'edit',
                                'id_side_plot_lines': id,
                                'wktString': wktString,
                            },
                            type: 'POST',
                            success: function (response) {
                                console.log(response);
                            }
                        });
                    });
                });


            } else {
                console.log('No lines to edit.');
            }
        });
        // END แก้ไข ขีดเขตแยก

        // Show ขีดเขตแยก
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/side_plot_lines/',
            data: {
                'id_time_line': id_time_line,
                'type_query': 'show',
            },
            type: 'POST',
            success: function (response) {
                // console.log(response);
                // get geojson
                var geojson = response;

                // add geojson to map
                L.geoJson(geojson, {
                    pane: 'pane_side_plot_lines',
                    onEachFeature: function (feature, layer) {
                        console.log(feature);
                        // add property to layer
                        layer.properties = feature.properties;
                        // Add the line to the layer group
                        sidePlotLinesLayerGroup.addLayer(layer);
                    }
                });
                // change style
                sidePlotLinesLayerGroup.eachLayer(function(layer) {
                    layer.setStyle({
                        color: '#000000',
                        weight: 1,
                        opacity: 1,
                    });
                });
            }
        });
        // END Show ขีดเขตแยก

        // delete ขีดเขตแยก
        $('#del_side_plot_lines').click(function () {
            console.log('Delete');
            // If there are lines added, enable editing for those
            if (sidePlotLinesLayerGroup.getLayers().length > 0) {
                // Disable global edit mode to ensure we're only editing the target layer group
                map.pm.disableGlobalEditMode();

                // Enable delete mode for each line in the layer group
                sidePlotLinesLayerGroup.eachLayer(function(layer) {
                    // enable removal mode like this:
                    map.pm.enableGlobalRemovalMode();

                    // listen to when a layer is removed
                    layer.on('pm:remove', (e) => {
                        // get properties id
                        var id = layer.properties.id;
                        // ajax delete line side_plot_lines
                        $.ajax({
                            // add csrf token
                            headers: { "X-CSRFToken": "{{ csrf_token }}" },
                            url: '/side_plot_lines/',
                            data: {
                                'id_time_line': id_time_line,
                                'type_query': 'delete',
                                'id_side_plot_lines': id,
                            },
                            type: 'POST',
                            success: function (response) {
                                console.log(response);
                            }
                        });
                    });

                });
            }

        });
        // END delete ขีดเขตแยก

        // ปุ่ม cancel ยกเลิก
        $('#cancle_all').click(function () {
            console.log('Cancel');
            // hide edit_pin_label
            $('#edit_pin_label_textarea').hide();
            // disable Draw Mode
            map.pm.disableDraw();
            // Disable global edit mode
            map.pm.disableGlobalEditMode();

            // Disable edit mode for all layers
            map.eachLayer(function(layer) {
                if (layer instanceof L.PM.Edit.Line) {
                    layer.pm.disable();
                }
            });

            // Clear the layer group
            // sidePlotLinesLayerGroup.clearLayers();
        });
        // End ปุ่ม cancel ยกเลิก



        // กรณีคลิกเปลี่ยน Scale แผนที่
        var polygon_scale;
        $('#scale_select').change(function() {
            var scale = $(this).val();
            console.log(scale);

            // Calculate distances
            var distance_mm_hor = scale * 172; // คือขนาดความกว้างของแผนที่แนวนอน 172 mm
            var distance_m_hor = distance_mm_hor / 1000; // Convert mm to meters
            var distance_mm_ver = scale * 118; // คือขนาดความสูงของแผนที่แนวตั้ง 118 mm
            var distance_m_ver = distance_mm_ver / 1000; // Convert mm to meters


            // Get center of shape rent layer
            var center_shp_rent = shp_rent.getBounds().getCenter();

            // Determine UTM zone based on longitude
            var utm_zone = center_shp_rent.lng < 102 ? 'WGS84 UTM ZONE47N' : 'WGS84 UTM ZONE48N';
            var converted_en = proj4('WGS84', utm_zone, [center_shp_rent.lng, center_shp_rent.lat]);
            var center_e_con = converted_en[0];
            var center_n_con = converted_en[1];

            // Remove previous polygon layer if exists
            if (polygon_scale) {
                map.removeLayer(polygon_scale);
            }

            // Calculate line lengths and coordinates for points
            var lineLengthHor = distance_m_hor / 2;
            var lineLengthVer = distance_m_ver / 2;


            var eastPoint = proj4(utm_zone, 'WGS84', [center_e_con + lineLengthHor, center_n_con - lineLengthVer]);
            var westPoint = proj4(utm_zone, 'WGS84', [center_e_con - lineLengthHor, center_n_con + lineLengthVer]);
            var northPoint = proj4(utm_zone, 'WGS84', [center_e_con+ lineLengthHor, center_n_con + lineLengthVer]);
            var southPoint = proj4(utm_zone, 'WGS84', [center_e_con - lineLengthHor, center_n_con - lineLengthVer]);

            // Draw new polygon
            polygon_scale = L.polygon([
                [northPoint[1], northPoint[0]],
                [eastPoint[1], eastPoint[0]],
                [southPoint[1], southPoint[0]],
                [westPoint[1], westPoint[0]]
            ], {
                color: 'black',
                weight: 1,
                opacity: 1,
                fillOpacity: 0

            }).addTo(map);
            // pane
            polygon_scale.options.pane = 'pane_polygon_scale';
            // Fit bounds to new polygon
            map.fitBounds(polygon_scale.getBounds());
            // get zoom level
            var zoom = map.getZoom();
            // ajax edit scale
            $.ajax({
                // add csrf token
                headers: { "X-CSRFToken": "{{ csrf_token }}" },
                url: '/scale_map/',
                data: {
                    'id_time_line': id_time_line,
                    'scale_map': scale,
                    'type_query': 'edit',
                },
                type: 'POST',
                success: function (response) {
                    console.log(response);
                }
            });




        });

        // End กรณีคลิกเปลี่ยน Scale แผนที่

        // Show scale
        var scale;
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/scale_map/',
            data: {
                'id_time_line': id_time_line,
                'type_query': 'get',
            },
            type: 'POST',
            success: function (response) {
                // console.log(response);
                scale = response;

                // Calculate distances
                var distance_mm_hor = scale * 172; // คือขนาดความกว้างของแผนที่แนวนอน 172 mm
                var distance_m_hor = distance_mm_hor / 1000; // Convert mm to meters
                var distance_mm_ver = scale * 118; // คือขนาดความสูงของแผนที่แนวตั้ง 118 mm
                var distance_m_ver = distance_mm_ver / 1000; // Convert mm to meters


                // Get center of shape rent layer
                var center_shp_rent = shp_rent.getBounds().getCenter();

                // Determine UTM zone based on longitude
                var utm_zone = center_shp_rent.lng < 102 ? 'WGS84 UTM ZONE47N' : 'WGS84 UTM ZONE48N';
                var converted_en = proj4('WGS84', utm_zone, [center_shp_rent.lng, center_shp_rent.lat]);
                var center_e_con = converted_en[0];
                var center_n_con = converted_en[1];

                // Remove previous polygon layer if exists
                if (polygon_scale) {
                    map.removeLayer(polygon_scale);
                }

                // Calculate line lengths and coordinates for points
                var lineLengthHor = distance_m_hor / 2;
                var lineLengthVer = distance_m_ver / 2;


                var eastPoint = proj4(utm_zone, 'WGS84', [center_e_con + lineLengthHor, center_n_con - lineLengthVer]);
                var westPoint = proj4(utm_zone, 'WGS84', [center_e_con - lineLengthHor, center_n_con + lineLengthVer]);
                var northPoint = proj4(utm_zone, 'WGS84', [center_e_con+ lineLengthHor, center_n_con + lineLengthVer]);
                var southPoint = proj4(utm_zone, 'WGS84', [center_e_con - lineLengthHor, center_n_con - lineLengthVer]);

                // Draw new polygon
                polygon_scale = L.polygon([
                    [northPoint[1], northPoint[0]],
                    [eastPoint[1], eastPoint[0]],
                    [southPoint[1], southPoint[0]],
                    [westPoint[1], westPoint[0]]
                ], {
                    color: 'black',
                    weight: 1,
                    opacity: 1,
                    fillOpacity: 0

                }).addTo(map);
                // pane
                polygon_scale.options.pane = 'pane_polygon_scale';
                // Fit bounds to new polygon
                map.fitBounds(polygon_scale.getBounds());
                // get zoom level
                var zoom = map.getZoom();
                // convert scale round 2 decimal
                var scale = parseFloat(response).toFixed(0);
                // console.log(scale);
                // set value to select
                $('#scale_select').val(scale);

            }
        });
        // End Show scale


        // วงหมุดเลขแปลง
        var circle_parcel;
        // add group layer
        var circle_parcel_layer = L.layerGroup().addTo(map);
        // add layer to layer control checked box
        layerControl.addOverlay(circle_parcel_layer, "วงหมุดเลขแปลง");
        // ปุ่ม เพิ่ม วงหมุดเลขแปลง
        $('#add_no_parcel').click(function () {
            // Create a new pane for top-most layer rendering
            var topPane = map.createPane('pane_circle_parcel'); // หมุดหลักเขต
            topPane.style.zIndex = 651;  // Higher than the overlay pane which is 400 by default
            // ajax
            $.ajax({
                // add csrf token
                headers: { "X-CSRFToken": "{{ csrf_token }}" },
                url: '/ajax_parcel_number/',
                data: {
                    'id_time_line': id_time_line,
                    'type': 'no_parcel',
                    'type_query': 'add',
                    'scale_map': scale,
                },
                type: 'POST',
                success: function (response) {
                    // if circle parcel > 0 remove layer
                    if (circle_parcel_layer.getLayers().length > 0) {
                        circle_parcel_layer.clearLayers();
                    }
                    // console.log(response);
                    // get geojson
                    var geojson = response;
                    // add geojson to map
                    L.geoJson(geojson, {
                        onEachFeature: function (feature, layer) {
                            // pane
                            layer.options.pane = 'pane_circle_parcel';
                            // add property to layer
                            layer.properties = feature.properties;
                            // console.log(layer.properties.content);
                            var svgContent = `
                            <svg height="100" width="100" xmlns="http://www.w3.org/2000/svg">
                                <style>
                                    @font-face {
                                        font-family: 'THSarabun';
                                        src: url(${fontBase64}) format('woff');
                                    }
                                    text {
                                        font-family: 'THSarabun';
                                        font-size: 40px;
                                        fill: black;
                                        dominant-baseline: middle;
                                        text-anchor: middle;
                                    }
                                </style>
                                <circle r="45" cx="50" cy="50" fill="white" stroke="black" stroke-width="3" />
                                <text x="50%" y="50%">${layer.properties.content}</text>
                            </svg>`;
                            // Encode SVG content as base64
                            var svgDataUri = 'data:image/svg+xml;base64,' + btoa(unescape(encodeURIComponent(svgContent)));
                            // Define custom icon using the SVG Data URI
                            var myIcon = L.icon({
                                iconUrl: svgDataUri,
                                iconSize: [70, 70], // Size of the icon
                                iconAnchor: [35, 35], // Center of the icon
                                popupAnchor: [0, -30] // Popup anchor point
                            });
                            // add to map
                            layer.setIcon(myIcon);
                            // Add the line to the layer group
                            circle_parcel_layer.addLayer(layer);

                        }

                    });


                }
            });

        });

        // ปุ่ม แก้ไข วงหมุดเลขแปลง
        $('#move_no_parcel').click(function () {
            console.log('edit_circle_parcel');
            // edit move circle_parcel_layer layer
            circle_parcel_layer.eachLayer(function(layer) {
                // enable dragging
                layer.dragging.enable();
                // snap to layer
                layer.pm.enable({
                    snappable: false,
                    snapDistance: 20,
                    snapSegment: true,
                    cursorMarker: true,
                });
                // listen to when a layer is changed in Edit Mode
                layer.on("pm:edit", (e) => {
                    // get properties id
                    var id = layer.properties.id;
                    // get contents
                    var contents = layer.properties.content;
                    // get latlng
                    var latlng = layer.getLatLng();
                    // ajax edit circle parcel
                    $.ajax({
                        // add csrf token
                        headers: { "X-CSRFToken": "{{ csrf_token }}" },
                        url: '/ajax_parcel_number/',
                        data: {
                            'id_time_line': id_time_line,
                            'id_text_map': id,
                            'type_query': 'edit',
                            'lat': latlng.lat,
                            'lng': latlng.lng,
                            'content': contents,
                        },
                        type: 'POST',
                        success: function (response) {
                            console.log(response);
                        }
                    });
                });
            });
        });

        // ปุ่ม ลบ วงหมุดเลขแปลง
        $('#del_no_parcel').click(function () {
            console.log('del_circle_parcel');
            // enable removal mode like this select layer circle_parcel_layer only
            map.pm.enableGlobalRemovalMode({
                layer: circle_parcel_layer,
            });
            // enable removal layer circle_parcel_layer
            circle_parcel_layer.eachLayer(function(layer) {

                // listen to when a layer is removed
                layer.on('pm:remove', (e) => {

                    // get properties id
                    var id = layer.properties.id;
                    // ajax delete circle parcel
                    $.ajax({
                        // add csrf token
                        headers: { "X-CSRFToken": "{{ csrf_token }}" },
                        url: '/ajax_parcel_number/',
                        data: {
                            'id_time_line': id_time_line,
                            'id_text_map': id,
                            'type_query': 'delete',
                        },
                        type: 'POST',
                        success: function (response) {
                            console.log(response);
                        }
                    });
                });
            });

        });

        // End วงหมุดเลขแปลง

// ------------------------------------------------------------------------------

        // Start ข้อความบนแผนที่
        // pane pane_text_map
        map.createPane('pane_text_map');
        map.getPane('pane_text_map').style.zIndex = 951;
        // layer group text_map_group
        var text_map_group = L.layerGroup().addTo(map);
        // add layer to layer control checked box
        layerControl.addOverlay(text_map_group, "ข้อความบนแผนที่");

        // Variable to track active text menu mode
        var activeTextMenuMode = null;

        // Function to disable all active text menu modes
        function disableAllTextMenuModes() {
            // Common cleanup for all modes
            // Disable global edit and removal modes
            map.pm.disableGlobalEditMode();
            map.pm.disableGlobalRemovalMode();

            // Reset all layers to their default state
            text_map_group.eachLayer(function(layer) {
                // Disable dragging for all layers
                if (layer.dragging) {
                    layer.dragging.disable();
                }

                // Disable pm editing for all layers
                if (layer.pm) {
                    layer.pm.disable();
                }

                // Remove all common events
                layer.off('click');
                layer.off('pm:edit');
                layer.off('pm:remove');

                // Reset icon to normal state if properties exist
                if (layer.properties) {
                    layer.setIcon(L.divIcon({
                        className: 'text_map2mm',
                        html: `
                            <style>
                                @font-face {
                                    font-family: 'THSarabun';
                                    src: url(${fontBase64}) format('woff');
                                }
                            </style>
                            <div style="font-size: ${layer.properties.size}px; transform: rotate(${layer.properties.rotate}deg); line-height: 14px; white-space: nowrap; font-family: 'THSarabun';">
                                ${layer.properties.content}
                            </div>
                        `,
                        iconSize: [100, 30]
                    }));
                }
            });

            // Mode-specific cleanup
            if (activeTextMenuMode === 'properties') {
                // Unbind all event handlers
                $('#id_text_map').off('keyup');
                $('#font_size_slider').off('input');
                $('#rotation_slider').off('input');
            }
            else if (activeTextMenuMode === 'copy' || activeTextMenuMode === 'add') {
                // Remove map click event
                map.off('click');
            }

            // Reset active mode
            activeTextMenuMode = null;
        }

        // Add an event listener for the 'contextmenu' event
        map.on('contextmenu', function(e) {
            alert("Right-click at " + e.latlng.toString());
            // You can also do other things here, like opening a custom context menu
            // or performing other actions
        });


        // When clicking the add text menu item
        $('#add_text_map').click(function() {
            // Disable any active menu mode first
            disableAllTextMenuModes();
        });

        // เมื่อคลิกปุ่มยืนยันใน modal เพิ่มข้อความบนแผนที่
        $('#confirm_text').click(function () {
            // Disable any active menu mode first
            disableAllTextMenuModes();

            // Set current mode
            activeTextMenuMode = 'add';

            // ดึงข้อความจาก input
            var contents = $('#input_text_map').val();
            // get type from dropdown
            var type = $('#dropdown_text_map').val();
            // console.log(id_time_line);
            // ตรวจสอบว่ามีข้อความหรือไม่
            if (contents) {
                // รอการคลิกบนแผนที่
                map.on('click', function(e) {
                    var latlng = e.latlng;
                    map.off('click');
                    // ajax save text map
                    $.ajax({
                        // add csrf token
                        headers: { "X-CSRFToken": "{{ csrf_token }}" },
                        url: '/case_text_map/',
                        data: {
                            'id_time_line': id_time_line,
                            'lat': latlng.lat,
                            'lng': latlng.lng,
                            'content': contents,
                            'type': type,
                            'case': 'add',
                        },
                        type: 'POST',
                        success: function (response) {
                            // console.log(response);
                            // oneach feature
                            L.geoJson(response, {
                                pane: 'pane_text_map',
                                onEachFeature: function (feature, layer) {
                                    // สร้าง text icon จากข้อความที่ผู้ใช้ป้อน
                                    var textIcon = L.divIcon({
                                        className: 'text_map2mm',
                                        html: `
                                            <style>
                                                @font-face {
                                                    font-family: 'THSarabun';
                                                    src: url(${fontBase64}) format('woff');
                                                }
                                            </style>
                                            <div style="font-size: ${feature.properties.size}px; transform: rotate(${feature.properties.rotate}deg); line-height: 14px; white-space: nowrap; font-family: 'THSarabun';">
                                                ${feature.properties.content}
                                            </div>
                                        `,
                                        iconSize: [100, 30]
                                    });
                                    // สร้าง marker ด้วย text icon
                                    var textMarker = L.marker([feature.geometry.coordinates[1], feature.geometry.coordinates[0]], {
                                        icon: textIcon,
                                        pane: 'pane_text_map'
                                    });
                                    // Initialize properties object if it doesn't exist
                                    textMarker.properties = {};

                                    // เพิ่ม properties จาก feature ลงใน marker
                                    textMarker.properties.id = feature.properties.id;
                                    textMarker.properties.content = feature.properties.content;
                                    textMarker.properties.size = feature.properties.size;
                                    textMarker.properties.rotate = feature.properties.rotate;
                                    textMarker.properties.type = feature.properties.type;

                                    // เพิ่ม marker ลงใน text_map_group
                                    text_map_group.addLayer(textMarker);
                                }
                            });

                        }
                    });
                });
            } else {
                alert('กรุณาป้อนข้อความก่อนยืนยัน');
            }
        });

        // แสดงข้อความบนแผนที่ เมื่อโหลดหน้าเว็บ
        $.ajax({
            // add csrf token
            headers: { "X-CSRFToken": "{{ csrf_token }}" },
            url: '/case_text_map/',
            data: {
                'id_time_line': id_time_line,
                'type': 'text_map', // แสดงข้อความแผนที่ฐานเท่านั้น
                'case': 'show',
            },
            type: 'POST',
            success: function (response) {
                // console.log(response);
                // oneach feature
                L.geoJson(response, {
                    pane: 'pane_text_map',
                    onEachFeature: function (feature, layer) {
                        // สร้าง text icon จากข้อความที่ผู้ใช้ป้อน
                        var textIcon = L.divIcon({
                            className: 'text_map2mm',
                            html: `
                                <style>
                                    @font-face {
                                        font-family: 'THSarabun';
                                        src: url(${fontBase64}) format('woff');
                                    }
                                </style>
                                <div style="font-size: ${feature.properties.size}px; transform: rotate(${feature.properties.rotate}deg); line-height: 14px; white-space: nowrap; font-family: 'THSarabun';">
                                    ${feature.properties.content}
                                </div>
                            `,
                            iconSize: [100, 30]
                        });
                        // สร้าง marker ด้วย text icon
                        var textMarker = L.marker([feature.geometry.coordinates[1], feature.geometry.coordinates[0]], {
                            icon: textIcon,
                            pane: 'pane_text_map'
                        });
                        // Initialize properties object if it doesn't exist
                        textMarker.properties = {};

                        // เพิ่ม properties จาก feature ลงใน marker
                        textMarker.properties.id = feature.properties.id;
                        textMarker.properties.content = feature.properties.content;
                        textMarker.properties.size = feature.properties.size;
                        textMarker.properties.rotate = feature.properties.rotate;
                        textMarker.properties.type = feature.properties.type;

                        // เพิ่ม marker ลงใน text_map_group
                        text_map_group.addLayer(textMarker);
                    }
                });
            }
        });


        // คลิกดู properties ของข้อความบนแผนที่
        $('#text_map_modal').click(function () {
            // Disable any active menu mode first
            disableAllTextMenuModes();

            // Set current mode
            activeTextMenuMode = 'properties';

            if (text_map_group.getLayers().length > 0) {

                // Variable to store the currently selected layer
                var activeLayer = null;

                // Function to unbind all previous event handlers
                function unbindPreviousEventHandlers() {
                    $('#id_text_map').off('keyup');
                    $('#font_size_slider').off('input');
                    $('#rotation_slider').off('input');
                }

                //  event click on text_map_group
                text_map_group.eachLayer(function(layer) {
                    // get type from layer properties
                    var type = layer.properties.type;
                    // click on text_map_group
                    layer.on('click', function(e) {
                        // Remove highlight from previously selected layer if exists
                        if (activeLayer && activeLayer !== layer) {
                            // Remove highlight styling from previously selected layer
                            activeLayer.setIcon(L.divIcon({
                                className: 'text_map2mm',
                                html: `
                                    <style>
                                        @font-face {
                                            font-family: 'THSarabun';
                                            src: url(${fontBase64}) format('woff');
                                        }
                                    </style>
                                    <div style="font-size: ${activeLayer.properties.size}px; transform: rotate(${activeLayer.properties.rotate}deg); line-height: 14px; white-space: nowrap; font-family: 'THSarabun';">
                                        ${activeLayer.properties.content}
                                    </div>
                                `,
                                iconSize: [100, 30]
                            }));
                        }

                        // Store the currently selected layer
                        activeLayer = layer;

                        // Unbind previous event handlers before adding new ones
                        unbindPreviousEventHandlers();

                        // set value to input text map
                        $('#id_text_map').val(layer.properties.content);
                        // set value to font size slider and input
                        $('#font_size_slider').val(layer.properties.size);
                        $('#font_size_input').val(layer.properties.size);
                        // set value to rotation slider and input
                        $('#rotation_slider').val(layer.properties.rotate);
                        $('#rotation_input').val(layer.properties.rotate);

                        // Highlight the selected layer with a red border
                        layer.setIcon(L.divIcon({
                            className: 'text_map2mm',
                            html: `
                                <style>
                                    @font-face {
                                        font-family: 'THSarabun';
                                        src: url(${fontBase64}) format('woff');
                                    }
                                </style>
                                <div style="font-size: ${layer.properties.size}px; transform: rotate(${layer.properties.rotate}deg); line-height: 14px; white-space: nowrap; font-family: 'THSarabun'; color: red; border: 2px dashed red; padding: 2px;">
                                    ${layer.properties.content}
                                </div>
                            `,
                            iconSize: [100, 30]
                        }));

                        // change content dynamic keyup - bind specifically to this layer
                        $('#id_text_map').on('keyup', function() {
                            // safety check
                            if (!activeLayer) return;

                            // get value from input
                            var content = $(this).val();
                            // update the layer property
                            activeLayer.properties.content = content;
                            // set content to layer with highlight styling
                            activeLayer.setIcon(L.divIcon({
                                className: 'text_map2mm',
                                html: `
                                    <style>
                                        @font-face {
                                            font-family: 'THSarabun';
                                            src: url(${fontBase64}) format('woff');
                                        }
                                    </style>
                                    <div style="font-size: ${activeLayer.properties.size}px; transform: rotate(${activeLayer.properties.rotate}deg); line-height: 14px; white-space: nowrap; font-family: 'THSarabun'; color: red; border: 2px dashed red; padding: 2px;">
                                        ${activeLayer.properties.content}
                                    </div>
                                `,
                                iconSize: [100, 30]
                            }));
                            // ajax edit text map
                            $.ajax({
                                // add csrf token
                                headers: { "X-CSRFToken": "{{ csrf_token }}" },
                                url: '/case_text_map/',
                                data: {
                                    'id_time_line': id_time_line,
                                    'id_text_map': id,
                                    'content': content,
                                    'type': type,
                                    'case': 'change_content',
                                },
                                type: 'POST',
                                success: function (response) {
                                    console.log(response);
                                }
                            });

                        });

                        //  chang dynamic font size - bind specifically to this layer
                        $('#font_size_slider').on('input', function() {
                            // safety check
                            if (!activeLayer) return;

                            // get value from slider
                            var fontSize = $(this).val();
                            // update the layer property
                            activeLayer.properties.size = fontSize;
                            // set value to input font size
                            $('#font_size_input').val(fontSize);
                            // set font size to layer with highlight styling
                            activeLayer.setIcon(L.divIcon({
                                className: 'text_map2mm',
                                html: `
                                    <style>
                                        @font-face {
                                            font-family: 'THSarabun';
                                            src: url(${fontBase64}) format('woff');
                                        }
                                    </style>
                                    <div style="font-size: ${fontSize}px; transform: rotate(${activeLayer.properties.rotate}deg); line-height: 14px; white-space: nowrap; font-family: 'THSarabun'; color: red; border: 2px dashed red; padding: 2px;">
                                        ${activeLayer.properties.content}
                                    </div>
                                `,
                                iconSize: [100, 30]
                            }));
                            // ajax edit text map
                            $.ajax({
                                // add csrf token
                                headers: { "X-CSRFToken": "{{ csrf_token }}" },
                                url: '/case_text_map/',
                                data: {
                                    'id_time_line': id_time_line,
                                    'id_text_map': id,
                                    'size': fontSize,
                                    'type': type,
                                    'case': 'change_size',
                                },
                                type: 'POST',
                                success: function (response) {
                                    console.log(response);
                                }
                            });

                        });

                        //  chang dynamic rotation - bind specifically to this layer
                        $('#rotation_slider').on('input', function() {
                            // safety check
                            if (!activeLayer) return;

                            // get value from slider
                            var rotation = $(this).val();
                            // update the layer property
                            activeLayer.properties.rotate = rotation;
                            // set value to input rotation
                            $('#rotation_input').val(rotation);
                            // set rotation to layer with highlight styling
                            activeLayer.setIcon(L.divIcon({
                                className: 'text_map2mm',
                                html: `
                                    <style>
                                        @font-face {
                                            font-family: 'THSarabun';
                                            src: url(${fontBase64}) format('woff');
                                        }
                                    </style>
                                    <div style="font-size: ${activeLayer.properties.size}px; transform: rotate(${rotation}deg); line-height: 14px; white-space: nowrap; font-family: 'THSarabun'; color: red; border: 2px dashed red; padding: 2px;">
                                        ${activeLayer.properties.content}
                                    </div>
                                `,
                                iconSize: [100, 30]
                            }));
                            // ajax edit text map
                            $.ajax({
                                // add csrf token
                                headers: { "X-CSRFToken": "{{ csrf_token }}" },
                                url: '/case_text_map/',
                                data: {
                                    'id_time_line': id_time_line,
                                    'id_text_map': id,
                                    'rotate': rotation,
                                    'type': type,
                                    'case': 'change_rotate',
                                },
                                type: 'POST',
                                success: function (response) {
                                    console.log(response);
                                }
                            });

                        });

                        // get id from layer properties
                        var id = layer.properties.id;
                        console.log(id);

                    });
                });
                // remove layer text_map_group
                // text_map_group.clearLayers();
            }

        });

        // ย้ายข้อความ text_map_group
        $('#text_move_menu').click(function () {
            // Disable any active menu mode first
            disableAllTextMenuModes();

            // Set current mode
            activeTextMenuMode = 'move';

            // move text_map_group layer
            text_map_group.eachLayer(function(layer) {
                // enable dragging
                layer.dragging.enable();
                // snap to layer
                layer.pm.enable({
                    snappable: false,
                    snapDistance: 20,
                    snapSegment: true,
                    cursorMarker: true,
                });
                // listen to when a layer is changed in Edit Mode
                layer.on("pm:edit", (e) => {
                    // get properties id
                    var id = layer.properties.id;
                    // get type
                    var type = layer.properties.type;
                    // get latlng
                    var latlng = layer.getLatLng();
                    console.log(latlng);
                    console.log(id);
                    // ajax edit text map
                    $.ajax({
                        // add csrf token
                        headers: { "X-CSRFToken": "{{ csrf_token }}" },
                        url: '/case_text_map/',
                        data: {
                            'id_time_line': id_time_line,
                            'id_text_map': id,
                            'lat': latlng.lat,
                            'lng': latlng.lng,
                            'type': type,
                            'case': 'move',
                        },
                        type: 'POST',
                        success: function (response) {
                            console.log(response);
                        }
                    });

                });
            });
        });


        // คัดลอกข้อความ text_map_group
        $('#text_copy_menu').click(function () {
            // Disable any active menu mode first
            disableAllTextMenuModes();

            // Set current mode
            activeTextMenuMode = 'copy';

            text_map_group.eachLayer(function(layer) {
                layer.on("click", (e) => {
                    // get properties id
                    var id = layer.properties.id;
                    // get type
                    var type = layer.properties.type;
                    console.log(layer.properties);
                    // click location for copy
                    map.on('click', function(e) {
                        var latlng = e.latlng;
                        map.off('click');
                        // ajax copy text map
                        $.ajax({
                            // add csrf token
                            headers: { "X-CSRFToken": "{{ csrf_token }}" },
                            url: '/case_text_map/',
                            data: {
                                'id_time_line': id_time_line,
                                'id_text_map': id,
                                'lat': latlng.lat,
                                'lng': latlng.lng,
                                'type': type,
                                'case': 'copy',
                            },
                            type: 'POST',
                            success: function (response) {
                                console.log(response);
                                // get geojson
                                var geojson = response;
                                // add geojson to map
                                L.geoJson(geojson, {
                                    pane: 'pane_text_map',
                                    onEachFeature: function (feature, layer) {
                                        // สร้าง text icon จากข้อความ
                                        var textIcon = L.divIcon({
                                            className: 'text_map2mm',
                                            html: `
                                                <style>
                                                    @font-face {
                                                        font-family: 'THSarabun';
                                                        src: url(${fontBase64}) format('woff');
                                                    }
                                                </style>
                                                <div style="font-size: ${feature.properties.size}px; transform: rotate(${feature.properties.rotate}deg); line-height: 14px; white-space: nowrap; font-family: 'THSarabun';">
                                                    ${feature.properties.content}
                                                </div>
                                            `,
                                            iconSize: [100, 30]
                                        });
                                        // สร้าง marker ด้วย text icon
                                        var textMarker = L.marker([feature.geometry.coordinates[1], feature.geometry.coordinates[0]], {
                                            icon: textIcon,
                                            pane: 'pane_text_map'
                                        });
                                        // Initialize properties object if it doesn't exist
                                        textMarker.properties = {};
                                        // เพิ่ม properties จาก feature ลงใน marker
                                        textMarker.properties.id = feature.properties.id;
                                        textMarker.properties.content = feature.properties.content;
                                        textMarker.properties.size = feature.properties.size;
                                        textMarker.properties.rotate = feature.properties.rotate;
                                        textMarker.properties.type = feature.properties.type;
                        

                                        // เพิ่ม marker ลงใน text_map_group
                                        text_map_group.addLayer(textMarker);


                                    }
                                });
                            }
                        });

                    });
                });
            });
        });

        // ลบข้อความ text_map_group
        $('#text_delete_menu').click(function () {
            // Disable any active menu mode first
            disableAllTextMenuModes();

            // Set current mode
            activeTextMenuMode = 'delete';

            // enable removal layer text_map_group and type = 'text_map'
            text_map_group.eachLayer(function(layer) {
                // layer pm remove
                layer.pm.enable({
                    snappable: false,
                    snapDistance: 20,
                    snapSegment: true,
                    cursorMarker: true,
                });
                // enable removal mode like this:
                map.pm.enableGlobalRemovalMode({
                    layer: text_map_group,
                });
                // listen to when a layer is removed
                layer.on('pm:remove', (e) => {
                    // get properties id
                    var id = layer.properties.id;
                    // ajax delete text map
                    $.ajax({
                        // add csrf token
                        headers: { "X-CSRFToken": "{{ csrf_token }}" },
                        url: '/case_text_map/',
                        data: {
                            'id_time_line': id_time_line,
                            'id_text_map': id,
                            'type': 'text_map',
                            'case': 'delete',
                        },
                        type: 'POST',
                        success: function (response) {
                            console.log(response);
                            // remove layer
                            layer.remove();
                        }
                    });
                });
            });

        });



        // End ข้อความบนแผนที่
        // ----------------------------------------------------------------------------------










    });

</script>


<!-- When the font_size_slider range is moved, update the font_size_input value -->
<script>
document.addEventListener('DOMContentLoaded', function() {
  // Font size slider and input synchronization
  const fontSizeSlider = document.getElementById('font_size_slider');
  const fontSizeInput = document.getElementById('font_size_input');

  if(fontSizeSlider && fontSizeInput) {
    fontSizeSlider.addEventListener('input', function() {
      fontSizeInput.value = this.value;
    });

    fontSizeInput.addEventListener('input', function() {
      fontSizeSlider.value = this.value;
    });
  }

  // Rotation slider and input synchronization
  const rotationSlider = document.getElementById('rotation_slider');
  const rotationInput = document.getElementById('rotation_input');

  if(rotationSlider && rotationInput) {
    rotationSlider.addEventListener('input', function() {
      rotationInput.value = this.value;
    });

    rotationInput.addEventListener('input', function() {
      rotationSlider.value = this.value;
    });
  }
});
</script>

{% endblock js %}